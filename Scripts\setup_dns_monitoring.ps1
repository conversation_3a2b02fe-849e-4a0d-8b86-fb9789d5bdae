# Complete DNS Monitoring Setup Script
# Sets up automated DNS cache collection for N8N DNS Security Monitor

Write-Host "N8N DNS Security Monitor Setup" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan
Write-Host ""

# Check if running in correct directory
if (-not (Test-Path "Scripts\get_dns_cache.ps1")) {
    Write-Host "❌ Please run this script from the N8N_Builder root directory" -ForegroundColor Red
    Write-Host "   Current location: $PWD" -ForegroundColor Yellow
    Write-Host "   Expected files: Scripts\get_dns_cache.ps1" -ForegroundColor Yellow
    exit 1
}

# Create dns_reports and setup directories if they don't exist
if (-not (Test-Path "data\dns_reports")) {
    Write-Host "Creating dns_reports directory..." -ForegroundColor Yellow
    New-Item -Path "data\dns_reports" -ItemType Directory -Force | Out-Null
    Write-Host "SUCCESS: Created: data\dns_reports\" -ForegroundColor Green
}

if (-not (Test-Path "data\dns_reports\setup")) {
    Write-Host "Creating setup directory..." -ForegroundColor Yellow
    New-Item -Path "data\dns_reports\setup" -ItemType Directory -Force | Out-Null
    Write-Host "SUCCESS: Created: data\dns_reports\setup\" -ForegroundColor Green
}

Write-Host "Choose DNS cache collection method:" -ForegroundColor White
Write-Host ""
Write-Host "1. Windows Task Scheduler (Recommended)" -ForegroundColor Green
Write-Host "   - Runs automatically every 60 minutes (5 min before N8N)" -ForegroundColor Gray
Write-Host "   - Starts with Windows" -ForegroundColor Gray
Write-Host "   - Runs in background" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Continuous Loop Script" -ForegroundColor Yellow
Write-Host "   - Runs in PowerShell window" -ForegroundColor Gray
Write-Host "   - Must keep window open" -ForegroundColor Gray
Write-Host "   - Good for testing" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Manual Collection Only" -ForegroundColor Blue
Write-Host "   - Run Scripts\get_dns_cache.ps1 manually" -ForegroundColor Gray
Write-Host "   - No automation" -ForegroundColor Gray
Write-Host ""
Write-Host "4. Remove Existing Automation" -ForegroundColor Red
Write-Host ""

$choice = Read-Host "Enter choice (1-4)"

switch ($choice) {
    "1" {
        Write-Host "`nSetting up Windows Task Scheduler..." -ForegroundColor Green
        try {
            & "Scripts\setup_dns_automation.ps1"
            Write-Host "`nSUCCESS: Task Scheduler setup complete!" -ForegroundColor Green
            Write-Host "   DNS cache will be collected automatically every 60 minutes" -ForegroundColor White
            Write-Host "   Windows task synchronized to run 5 minutes before N8N workflow" -ForegroundColor White
            Write-Host "   Check status: .\\Scripts\\setup_dns_automation.ps1 -Status" -ForegroundColor Gray
        } catch {
            Write-Host "ERROR: Error setting up Task Scheduler: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "   Try running PowerShell as Administrator" -ForegroundColor Yellow
        }
    }
    
    "2" {
        Write-Host "`nStarting continuous loop..." -ForegroundColor Yellow
        Write-Host "   This will run continuously until you close the window" -ForegroundColor Gray
        Write-Host "   Press Ctrl+C to stop" -ForegroundColor Gray
        Write-Host ""
        & "Scripts\dns_cache_loop.ps1"
    }
    
    "3" {
        Write-Host "`nManual mode selected" -ForegroundColor Blue
        Write-Host "   Run this command before each N8N workflow execution:" -ForegroundColor White
        Write-Host "   .\Scripts\get_dns_cache.ps1" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "   Testing manual collection now..." -ForegroundColor Yellow
        & "Scripts\get_dns_cache.ps1"
    }
    
    "4" {
        Write-Host "`nRemoving automation..." -ForegroundColor Red
        try {
            & "Scripts\setup_dns_automation.ps1" -Remove
            Write-Host "SUCCESS: Automation removed" -ForegroundColor Green
        } catch {
            Write-Host "WARNING: No automation found to remove" -ForegroundColor Yellow
        }
    }
    
    default {
        Write-Host "ERROR: Invalid choice. Please run the script again." -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Import the updated DNS workflow into N8N:" -ForegroundColor White
Write-Host "   data\dns_reports\setup\dns_security_workflow_fixed.json" -ForegroundColor Gray
Write-Host "2. The workflow will automatically read DNS cache data" -ForegroundColor White
Write-Host "3. Reports will be saved to: data\dns_reports\" -ForegroundColor White
Write-Host "4. View dashboard: data\dns_reports\index.html" -ForegroundColor White
Write-Host ""
Write-Host "DNS Security Monitoring is now configured!" -ForegroundColor Green
