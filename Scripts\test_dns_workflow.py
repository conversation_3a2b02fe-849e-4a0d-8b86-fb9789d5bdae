#!/usr/bin/env python3
"""
Test DNS Security Monitoring Workflow Generation
"""

import asyncio
import sys
import json
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from n8n_builder.n8n_builder import N8NBuilder

async def test_dns_workflow():
    """Test generating the DNS security monitoring workflow."""
    print("🔍 Testing DNS Security Monitoring Workflow Generation")
    print("=" * 60)
    
    # Initialize builder
    try:
        builder = N8NBuilder()
        print("✅ N8N Builder initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize N8N Builder: {e}")
        return
    
    # Test description
    description = """Create an N8N workflow that:
1. Runs every 60 minutes using a Schedule Trigger
2. Executes the Windows command 'ipconfig /displaydns' 
3. Parses the command output to extract domain names
4. Saves the domain list to a file for review
This is a basic DNS monitoring workflow."""
    
    print(f"Description: {description}")
    print("\nGenerating workflow...")
    
    try:
        # Generate the workflow
        workflow_result = builder.generate_workflow(
            plain_english_description=description
        )
        
        if workflow_result:
            print("✅ Workflow generated successfully!")
            print(f"Result length: {len(workflow_result)} characters")
            
            # Try to parse as JSON
            try:
                workflow_data = json.loads(workflow_result)
                print("✅ Valid JSON workflow generated")
                
                # Show basic info
                nodes = workflow_data.get('nodes', [])
                connections = workflow_data.get('connections', {})
                
                print(f"   Nodes: {len(nodes)}")
                print(f"   Connections: {len(connections)}")
                
                # Show node types
                if nodes:
                    print("   Node types:")
                    for i, node in enumerate(nodes, 1):
                        node_type = node.get('type', 'Unknown')
                        node_name = node.get('name', f'Node {i}')
                        print(f"     {i}. {node_name} ({node_type})")
                
                # Save the workflow for inspection
                output_file = Path("data/generated_dns_workflow.json")
                output_file.parent.mkdir(exist_ok=True)
                
                with open(output_file, 'w') as f:
                    json.dump(workflow_data, f, indent=2)
                
                print(f"✅ Workflow saved to: {output_file}")
                
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ Generated result is not valid JSON: {e}")
                print(f"First 500 characters: {workflow_result[:500]}...")
                return False
                
        else:
            print("❌ No workflow generated")
            return False
            
    except Exception as e:
        print(f"❌ Workflow generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_dns_workflow())
