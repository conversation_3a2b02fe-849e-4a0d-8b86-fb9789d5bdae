# Update DNS Domain History CSV File
# This script processes DNS cache data and maintains historical domain records

param(
    [string]$DnsCacheFile = "data\dns_reports\setup\dns_cache_output.txt",
    [string]$HistoryFile = "data\dns_reports\setup\dns_domain_history.csv",
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

if ($Verbose) {
    Write-Host "[$(Get-Date)] Starting domain history update..." -ForegroundColor Cyan
}

try {
    # Ensure history file exists with headers
    if (-not (Test-Path $HistoryFile)) {
        $headers = "domain_name,ip_address,first_seen,last_seen,occurrence_count,current_risk_level,highest_risk_level,threat_categories,dns_record_type,created_date,modified_date"
        $headers | Out-File -FilePath $HistoryFile -Encoding UTF8
        if ($Verbose) {
            Write-Host "Created new history file: $HistoryFile" -ForegroundColor Green
        }
    }
    
    # Read DNS cache file
    if (-not (Test-Path $DnsCacheFile)) {
        throw "DNS cache file not found: $DnsCacheFile"
    }
    
    $dnsContent = Get-Content $DnsCacheFile -Raw
    if ($Verbose) {
        Write-Host "Read DNS cache file: $DnsCacheFile" -ForegroundColor White
        Write-Host "Content length: $($dnsContent.Length) characters" -ForegroundColor White
    }
    
    # Extract domains from DNS cache
    $domains = @()
    $domainPattern = 'Record Name[^:]*:\s*([^\s\r\n]+)'
    $matches = [regex]::Matches($dnsContent, $domainPattern)
    
    foreach ($match in $matches) {
        $domain = $match.Groups[1].Value.Trim().ToLower()
        if ($domain -and $domain.Contains('.') -and -not $domain.Contains('localhost') -and -not ($domain -match '^\d+\.\d+\.\d+\.\d+$')) {
            $domains += $domain
        }
    }
    
    $uniqueDomains = $domains | Sort-Object | Get-Unique
    if ($Verbose) {
        Write-Host "Found $($uniqueDomains.Count) unique domains" -ForegroundColor White
    }
    
    # Read existing history
    $existingHistory = @{}
    if ((Get-Item $HistoryFile).Length -gt 0) {
        $historyData = Import-Csv $HistoryFile
        foreach ($record in $historyData) {
            $existingHistory[$record.domain_name] = $record
        }
        if ($Verbose) {
            Write-Host "Loaded $($existingHistory.Count) existing domain records" -ForegroundColor White
        }
    }
    
    # Update history records
    $currentTime = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    $updatedRecords = @()
    $newDomains = 0
    $updatedDomains = 0
    
    # Process existing domains first
    foreach ($existingDomain in $existingHistory.Keys) {
        $record = $existingHistory[$existingDomain]
        
        if ($uniqueDomains -contains $existingDomain) {
            # Domain still active - update last_seen and increment count
            $record.last_seen = $currentTime
            $record.occurrence_count = [int]$record.occurrence_count + 1
            $record.modified_date = $currentTime
            $updatedDomains++
        }
        
        $updatedRecords += $record
    }
    
    # Add new domains
    foreach ($domain in $uniqueDomains) {
        if (-not $existingHistory.ContainsKey($domain)) {
            $newRecord = [PSCustomObject]@{
                domain_name = $domain
                ip_address = ""
                first_seen = $currentTime
                last_seen = $currentTime
                occurrence_count = 1
                current_risk_level = "LOW"
                highest_risk_level = "LOW"
                threat_categories = ""
                dns_record_type = "A"
                created_date = $currentTime
                modified_date = $currentTime
            }
            $updatedRecords += $newRecord
            $newDomains++
        }
    }
    
    # Write updated history back to file
    $updatedRecords | Export-Csv -Path $HistoryFile -NoTypeInformation -Encoding UTF8
    
    if ($Verbose) {
        Write-Host "Domain history update complete:" -ForegroundColor Green
        Write-Host "  New domains: $newDomains" -ForegroundColor White
        Write-Host "  Updated domains: $updatedDomains" -ForegroundColor White
        Write-Host "  Total domains: $($updatedRecords.Count)" -ForegroundColor White
        Write-Host "  History file: $HistoryFile" -ForegroundColor White
    }
    
    # Return summary for N8N integration
    Write-Output "DOMAIN_HISTORY_UPDATE_SUCCESS"
    Write-Output "NEW_DOMAINS:$newDomains"
    Write-Output "UPDATED_DOMAINS:$updatedDomains"
    Write-Output "TOTAL_DOMAINS:$($updatedRecords.Count)"
    Write-Output "TIMESTAMP:$currentTime"
    
} catch {
    Write-Error "Domain history update failed: $($_.Exception.Message)"
    Write-Output "DOMAIN_HISTORY_UPDATE_FAILED"
    Write-Output "ERROR:$($_.Exception.Message)"
    exit 1
}
