# Continuous DNS cache collection loop
# Alternative to Task Scheduler - runs continuously in background

param(
    [int]$IntervalMinutes = 60,
    [switch]$Once
)

$ScriptPath = Join-Path $PSScriptRoot "get_dns_cache.ps1"

Write-Host "N8N DNS Cache Continuous Collector" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host "Interval: $IntervalMinutes minutes" -ForegroundColor White
Write-Host "Script: $ScriptPath" -ForegroundColor White

if ($Once) {
    Write-Host "Running once..." -ForegroundColor Yellow
    & $ScriptPath
    exit
}

Write-Host "Starting continuous collection..." -ForegroundColor Green
Write-Host "Press Ctrl+C to stop" -ForegroundColor Yellow
Write-Host ""

$runCount = 0

try {
    while ($true) {
        $runCount++
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        
        Write-Host "[$timestamp] Run #$runCount - Collecting DNS cache..." -ForegroundColor Cyan
        
        try {
            & $ScriptPath
            Write-Host "[$timestamp] ✅ DNS cache collection completed" -ForegroundColor Green
        } catch {
            Write-Host "[$timestamp] ❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        $nextRun = (Get-Date).AddMinutes($IntervalMinutes)
        Write-Host "[$timestamp] Next run at: $($nextRun.ToString('HH:mm:ss'))" -ForegroundColor White
        Write-Host ""
        
        # Sleep for the specified interval
        Start-Sleep -Seconds ($IntervalMinutes * 60)
    }
} catch {
    Write-Host "`nStopping DNS cache collector..." -ForegroundColor Yellow
    Write-Host "Total runs completed: $runCount" -ForegroundColor White
}
