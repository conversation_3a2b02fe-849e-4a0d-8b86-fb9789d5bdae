# Get Windows DNS cache and save to shared file for N8N
# This script runs on Windows host and saves DNS data where <PERSON><PERSON><PERSON> can access it

Write-Host "Getting Windows DNS cache..." -ForegroundColor Cyan

try {
    # Get DNS cache using ipconfig
    $dnsOutput = ipconfig /displaydns
    
    # Ensure setup directory exists
    $setupDir = "data\dns_reports\setup"
    if (-not (Test-Path $setupDir)) {
        New-Item -Path $setupDir -ItemType Directory -Force | Out-Null
        Write-Host "Created setup directory: $setupDir" -ForegroundColor Yellow
    }

    # Create output file in the setup directory
    $outputFile = "dns_cache_output.txt"
    
    # Save DNS output to file
    $dnsOutput | Out-File -FilePath $outputFile -Encoding UTF8 -Force
    
    Write-Host "SUCCESS: DNS cache saved to: $outputFile" -ForegroundColor Green
    Write-Host "   File size: $((Get-Item $outputFile).Length) bytes" -ForegroundColor White
    Write-Host "   Lines: $(($dnsOutput | Measure-Object -Line).Lines)" -ForegroundColor White
    
    # Also create a JSON metadata file
    $metadata = @{
        timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        command = "ipconfig /displaydns"
        outputFile = $outputFile
        fileSize = (Get-Item $outputFile).Length
        lineCount = ($dnsOutput | Measure-Object -Line).Lines
        status = "success"
    }
    
    $metadataFile = "dns_cache_metadata.json"
    $metadata | ConvertTo-Json | Out-File -FilePath $metadataFile -Encoding UTF8 -Force
    
    Write-Host "SUCCESS: Metadata saved to: $metadataFile" -ForegroundColor Green
    
    # Show sample of DNS data
    Write-Host "`nSample DNS entries found:" -ForegroundColor Yellow
    $dnsOutput | Select-String "Record Name" | Select-Object -First 5 | ForEach-Object {
        $domain = ($_ -split ":")[1].Trim()
        if ($domain -and $domain -ne "" -and $domain.Contains(".")) {
            Write-Host "   - $domain" -ForegroundColor White
        }
    }
    
} catch {
    Write-Host "ERROR: Error getting DNS cache: $($_.Exception.Message)" -ForegroundColor Red
    
    # Create error metadata
    $errorMetadata = @{
        timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        command = "ipconfig /displaydns"
        status = "error"
        error = $_.Exception.Message
    }
    
    $errorMetadata | ConvertTo-Json | Out-File -FilePath "dns_cache_metadata.json" -Encoding UTF8 -Force
    exit 1
}

    # Update domain history tracking
    Write-Host "`nUpdating domain history..." -ForegroundColor Yellow
    try {
        & "Scripts\update_domain_history.ps1" -Verbose
        Write-Host "SUCCESS: Domain history updated" -ForegroundColor Green
    } catch {
        Write-Host "WARNING: Domain history update failed: $($_.Exception.Message)" -ForegroundColor Yellow
    }

Write-Host "`nN8N can now read DNS data from: $outputFile" -ForegroundColor Green
