# Setup automated DNS cache collection using Windows Task Scheduler
# This creates a scheduled task that runs every 55 minutes (before N8N's 60-minute schedule)

param(
    [switch]$Remove,
    [switch]$Status
)

$TaskName = "N8N_DNS_Cache_Collector"
$ScriptPath = Join-Path $PSScriptRoot "get_dns_cache.ps1"
$ProjectRoot = Split-Path $PSScriptRoot -Parent

Write-Host "N8N DNS Cache Automation Setup" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

if ($Status) {
    # Check if task exists and show status
    try {
        $task = Get-ScheduledTask -TaskName $TaskName -ErrorAction Stop
        Write-Host "✅ Task Status: $($task.State)" -ForegroundColor Green
        Write-Host "   Last Run: $($task.LastRunTime)" -ForegroundColor White
        Write-Host "   Next Run: $($task.NextRunTime)" -ForegroundColor White
        Write-Host "   Script: $ScriptPath" -ForegroundColor White
        
        # Show recent runs
        $taskInfo = Get-ScheduledTaskInfo -TaskName $TaskName
        Write-Host "   Last Result: $($taskInfo.LastTaskResult)" -ForegroundColor White
        
    } catch {
        Write-Host "❌ Task not found: $TaskName" -ForegroundColor Red
    }
    exit
}

if ($Remove) {
    # Remove the scheduled task
    try {
        Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
        Write-Host "✅ Removed scheduled task: $TaskName" -ForegroundColor Green
    } catch {
        Write-Host "❌ Error removing task: $($_.Exception.Message)" -ForegroundColor Red
    }
    exit
}

# Create the scheduled task
Write-Host "Setting up automated DNS cache collection..." -ForegroundColor Yellow
Write-Host "Project Root: $ProjectRoot" -ForegroundColor White
Write-Host "Script Path: $ScriptPath" -ForegroundColor White

# Verify script exists
if (-not (Test-Path $ScriptPath)) {
    Write-Host "❌ Script not found: $ScriptPath" -ForegroundColor Red
    exit 1
}

try {
    # Remove existing task if it exists
    try {
        Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false -ErrorAction SilentlyContinue
        Write-Host "   Removed existing task" -ForegroundColor Yellow
    } catch {
        # Task didn't exist, that's fine
    }
    
    # Create the action (what to run)
    $Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-ExecutionPolicy Bypass -File `"$ScriptPath`"" -WorkingDirectory $ProjectRoot
    
    # Create the trigger (when to run) - every 60 minutes, starting 5 minutes from now
    # This ensures it runs 5 minutes before N8N's hourly schedule
    $startTime = (Get-Date).AddMinutes(5)
    $Trigger = New-ScheduledTaskTrigger -Once -At $startTime -RepetitionInterval (New-TimeSpan -Minutes 60) -RepetitionDuration (New-TimeSpan -Days 365)
    
    # Create the settings
    $Settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -RunOnlyIfNetworkAvailable
    
    # Create the principal (run as current user)
    $Principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive
    
    # Register the task
    Register-ScheduledTask -TaskName $TaskName -Action $Action -Trigger $Trigger -Settings $Settings -Principal $Principal -Description "Automatically collect DNS cache data for N8N DNS Security Monitor every 60 minutes (5 minutes before N8N workflow)"
    
    Write-Host "✅ Scheduled task created successfully!" -ForegroundColor Green
    Write-Host "   Task Name: $TaskName" -ForegroundColor White
    Write-Host "   Frequency: Every 60 minutes (5 min before N8N)" -ForegroundColor White
    Write-Host "   Next Run: $((Get-ScheduledTask -TaskName $TaskName).NextRunTime)" -ForegroundColor White
    
    # Test run the task immediately
    Write-Host "`nTesting task with immediate run..." -ForegroundColor Yellow
    Start-ScheduledTask -TaskName $TaskName
    
    Start-Sleep -Seconds 3
    
    # Check if DNS cache file was created
    $dnsFile = Join-Path $ProjectRoot "data\dns_reports\dns_cache_output.txt"
    if (Test-Path $dnsFile) {
        Write-Host "✅ Test run successful! DNS cache file created." -ForegroundColor Green
        Write-Host "   File: $dnsFile" -ForegroundColor White
        Write-Host "   Size: $((Get-Item $dnsFile).Length) bytes" -ForegroundColor White
    } else {
        Write-Host "⚠️  Test run completed but DNS cache file not found." -ForegroundColor Yellow
        Write-Host "   Expected: $dnsFile" -ForegroundColor White
    }
    
    Write-Host "`n🎯 Automation Setup Complete!" -ForegroundColor Green
    Write-Host "   • DNS cache collected every 60 minutes" -ForegroundColor White
    Write-Host "   • N8N workflow runs every 60 minutes" -ForegroundColor White
    Write-Host "   • Windows task runs 5 minutes before N8N (synchronized)" -ForegroundColor White
    Write-Host "   • Fresh DNS data will always be available" -ForegroundColor White
    Write-Host "`nManagement Commands:" -ForegroundColor Cyan
    Write-Host "   Status:  .\Scripts\setup_dns_automation.ps1 -Status" -ForegroundColor White
    Write-Host "   Remove:  .\Scripts\setup_dns_automation.ps1 -Remove" -ForegroundColor White
    
} catch {
    Write-Host "❌ Error creating scheduled task: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   You may need to run PowerShell as Administrator" -ForegroundColor Yellow
}
