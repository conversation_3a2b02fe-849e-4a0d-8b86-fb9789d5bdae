# Create dns_reports directory in shared volume
Write-Host "Creating dns_reports directory..." -ForegroundColor Cyan

Set-Location "n8n-docker"

# Create dns_reports and setup directories in the container's shared volume
Write-Host "Creating /home/<USER>/shared/dns_reports/ directory..." -ForegroundColor Yellow
try {
    docker-compose exec n8n mkdir -p /home/<USER>/shared/dns_reports/setup
    Write-Host "SUCCESS: Directories created in container" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Could not create directory in container: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Set proper permissions
Write-Host "Setting permissions..." -ForegroundColor Yellow
try {
    docker-compose exec n8n chown -R node:node /home/<USER>/shared/dns_reports
    docker-compose exec n8n chmod -R 755 /home/<USER>/shared/dns_reports
    Write-Host "SUCCESS: Permissions set" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Could not set permissions: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Verify directory exists on host
if (Test-Path "../data/dns_reports") {
    Write-Host "✅ Directory exists on host: ./data/dns_reports/" -ForegroundColor Green
} else {
    Write-Host "❌ Directory not found on host" -ForegroundColor Red
}

# Test write permissions
Write-Host "Testing write permissions..." -ForegroundColor Yellow
try {
    docker-compose exec n8n touch /home/<USER>/shared/dns_reports/test_permissions.txt
    if (Test-Path "../data/dns_reports/test_permissions.txt") {
        Write-Host "✅ Write permissions working!" -ForegroundColor Green
        Remove-Item "../data/dns_reports/test_permissions.txt" -Force
    } else {
        Write-Host "❌ Write permissions not working" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error testing permissions: $($_.Exception.Message)" -ForegroundColor Red
}

Set-Location ".."
Write-Host "dns_reports directory setup complete!" -ForegroundColor Green
